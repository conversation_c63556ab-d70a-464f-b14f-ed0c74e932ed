from django.http import HttpResponse, HttpResponseForbidden
from django.shortcuts import render
from django.http import JsonResponse
from .models import Notification

# Create your views here.

def index(request):
    return render(request, "authentication/login.html")

def dashboard(request):
    unread_notification = Notification.objects.filter(user=request.user, is_read=False)
    unread_notification_count = unread_notification.count()
    context = {
        'unread_notification': unread_notification,
        'unread_notification_count': unread_notification_count
    }
    return render(request, "students/student-dashboard.html", context)

def admin_dashboard(request):
    """Dashboard for admin users"""
    from student.models import Student
    unread_notification = Notification.objects.filter(user=request.user, is_read=False)
    unread_notification_count = unread_notification.count()
    total_students = Student.objects.count()

    context = {
        'unread_notification': unread_notification,
        'unread_notification_count': unread_notification_count,
        'total_students': total_students,
        'user_role': 'Admin'
    }
    return render(request, "students/admin-dashboard.html", context)

def teacher_dashboard(request):
    """Dashboard for teacher users"""
    from student.models import Student
    unread_notification = Notification.objects.filter(user=request.user, is_read=False)
    unread_notification_count = unread_notification.count()
    total_students = Student.objects.count()

    context = {
        'unread_notification': unread_notification,
        'unread_notification_count': unread_notification_count,
        'total_students': total_students,
        'user_role': 'Teacher'
    }
    return render(request, "students/teacher-dashboard.html", context)



def mark_notification_as_read(request):
    if request.method == 'POST':
        notification = Notification.objects.filter(user=request.user, is_read=False)
        notification.update(is_read=True)
        return JsonResponse({'status': 'success'})
    return HttpResponseForbidden()

def clear_all_notification(request):
    if request.method == "POST":
        notification = Notification.objects.filter(user=request.user)
        notification.delete()
        return JsonResponse({'status': 'success'})
    return HttpResponseForbidden()