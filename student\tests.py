from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from .models import Student, Parent
from school.models import Notification
from datetime import date

User = get_user_model()

class StudentModelTest(TestCase):
    """Test cases for Student model"""

    def setUp(self):
        """Set up test data"""
        self.parent = Parent.objects.create(
            father_name="<PERSON>",
            father_occupation="Engineer",
            father_mobile="1234567890",
            father_email="<EMAIL>",
            mother_name="<PERSON>",
            mother_occupation="Teacher",
            mother_mobile="0987654321",
            mother_email="<EMAIL>",
            present_address="123 Main St",
            permanent_address="123 Main St"
        )

    def test_student_creation(self):
        """Test student model creation"""
        student = Student.objects.create(
            first_name="Test",
            last_name="Student",
            student_id="STU001",
            gender="Male",
            date_of_birth=date(2000, 1, 1),
            student_class="10th Grade",
            religion="Christianity",
            joining_date=date.today(),
            mobile_number="1111111111",
            admission_number="ADM001",
            section="A",
            parent=self.parent
        )

        self.assertEqual(student.first_name, "Test")
        self.assertEqual(student.last_name, "Student")
        self.assertEqual(student.student_id, "STU001")
        self.assertTrue(student.slug)  # Slug should be auto-generated
        self.assertEqual(str(student), "Test Student (STU001)")

    def test_slug_generation(self):
        """Test automatic slug generation"""
        student = Student.objects.create(
            first_name="Test",
            last_name="Student",
            student_id="STU002",
            gender="Female",
            date_of_birth=date(2000, 1, 1),
            student_class="10th Grade",
            religion="Christianity",
            joining_date=date.today(),
            mobile_number="2222222222",
            admission_number="ADM002",
            section="B",
            parent=self.parent
        )

        expected_slug = "test-student-stu002"
        self.assertEqual(student.slug, expected_slug)

class ParentModelTest(TestCase):
    """Test cases for Parent model"""

    def test_parent_creation(self):
        """Test parent model creation"""
        parent = Parent.objects.create(
            father_name="John Smith",
            father_occupation="Doctor",
            father_mobile="1234567890",
            father_email="<EMAIL>",
            mother_name="Mary Smith",
            mother_occupation="Nurse",
            mother_mobile="0987654321",
            mother_email="<EMAIL>",
            present_address="456 Oak St",
            permanent_address="456 Oak St"
        )

        self.assertEqual(parent.father_name, "John Smith")
        self.assertEqual(parent.mother_name, "Mary Smith")
        self.assertEqual(str(parent), "John Smith & Mary Smith")

class StudentViewTest(TestCase):
    """Test cases for Student views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            is_admin=True
        )

        self.parent = Parent.objects.create(
            father_name="Test Father",
            father_occupation="Engineer",
            father_mobile="1234567890",
            father_email="<EMAIL>",
            mother_name="Test Mother",
            mother_occupation="Teacher",
            mother_mobile="0987654321",
            mother_email="<EMAIL>",
            present_address="Test Address",
            permanent_address="Test Address"
        )

        self.student = Student.objects.create(
            first_name="Test",
            last_name="Student",
            student_id="STU003",
            gender="Male",
            date_of_birth=date(2000, 1, 1),
            student_class="10th Grade",
            religion="Christianity",
            joining_date=date.today(),
            mobile_number="3333333333",
            admission_number="ADM003",
            section="A",
            parent=self.parent
        )

    def test_student_list_view(self):
        """Test student list view"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('student_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.student.first_name)

    def test_add_student_view_get(self):
        """Test add student view GET request"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('add_student'))
        self.assertEqual(response.status_code, 200)

    def test_view_student(self):
        """Test view student detail"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('view_student', kwargs={'slug': self.student.slug}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.student.first_name)

    def test_edit_student_view(self):
        """Test edit student view"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('edit_student', kwargs={'slug': self.student.slug}))
        self.assertEqual(response.status_code, 200)
