{% extends 'Home/base.html' %}
{% load static %}
{% block body %}

<div class="page-wrapper">
<div class="content container-fluid">

<div class="page-header">
<div class="row">
<div class="col-sm-12">
<h3 class="page-title">Welcome {{ user.first_name }}!</h3>
<ul class="breadcrumb">
<li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item active">Admin Dashboard</li>
</ul>
</div>
</div>
</div>

<div class="row">
<div class="col-xl-3 col-sm-6 col-12 d-flex">
<div class="card bg-nine w-100">
<div class="card-body">
<div class="db-widgets d-flex justify-content-between align-items-center">
<div class="db-icon">
<i class="fas fa-users"></i>
</div>
<div class="db-info">
<h3>{{ total_students }}</h3>
<h6>Total Students</h6>
</div>
</div>
</div>
</div>
</div>
<div class="col-xl-3 col-sm-6 col-12 d-flex">
<div class="card bg-six w-100">
<div class="card-body">
<div class="db-widgets d-flex justify-content-between align-items-center">
<div class="db-icon">
<i class="fas fa-bell"></i>
</div>
<div class="db-info">
<h3>{{ unread_notification_count }}</h3>
<h6>Notifications</h6>
</div>
</div>
</div>
</div>
</div>
<div class="col-xl-3 col-sm-6 col-12 d-flex">
<div class="card bg-seven w-100">
<div class="card-body">
<div class="db-widgets d-flex justify-content-between align-items-center">
<div class="db-icon">
<i class="fas fa-user-graduate"></i>
</div>
<div class="db-info">
<h3>Admin</h3>
<h6>Role</h6>
</div>
</div>
</div>
</div>
</div>
<div class="col-xl-3 col-sm-6 col-12 d-flex">
<div class="card bg-eight w-100">
<div class="card-body">
<div class="db-widgets d-flex justify-content-between align-items-center">
<div class="db-icon">
<i class="fas fa-cog"></i>
</div>
<div class="db-info">
<h3>Active</h3>
<h6>Status</h6>
</div>
</div>
</div>
</div>
</div>
</div>

<div class="row">
<div class="col-md-12 col-lg-6">
<div class="card card-chart">
<div class="card-header">
<div class="row align-items-center">
<div class="col-6">
<h5 class="card-title">Quick Actions</h5>
</div>
</div>
</div>
<div class="card-body">
<div class="row">
<div class="col-md-6">
<a href="{% url 'add_student' %}" class="btn btn-primary btn-block mb-3">
<i class="fas fa-plus"></i> Add Student
</a>
</div>
<div class="col-md-6">
<a href="{% url 'student_list' %}" class="btn btn-info btn-block mb-3">
<i class="fas fa-list"></i> View Students
</a>
</div>
</div>
</div>
</div>
</div>

<div class="col-md-12 col-lg-6">
<div class="card card-chart">
<div class="card-header">
<div class="row align-items-center">
<div class="col-6">
<h5 class="card-title">System Overview</h5>
</div>
</div>
</div>
<div class="card-body">
<p>Welcome to the Student Management System Admin Dashboard. From here you can:</p>
<ul>
<li>Manage student records</li>
<li>View system notifications</li>
<li>Monitor system activity</li>
<li>Generate reports</li>
</ul>
</div>
</div>
</div>
</div>

</div>
</div>

{% endblock %}
