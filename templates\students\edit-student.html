{% extends 'Home/base.html' %}
{% load static %}
{% block body %}
<div class="page-wrapper">
    <div class="content container-fluid">
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h3 class="page-title">Edit Students</h3>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/students">Students</a></li>
                        <li class="breadcrumb-item active">Edit Students</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="form-title"><span>Student Information</span></h5>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>First Name</label>
                                        <input type="text" name="first_name" class="form-control" value="{{ student.first_name }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Last Name</label>
                                        <input type="text" name="last_name" class="form-control" value="{{ student.last_name }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Student Id</label>
                                        <input type="text" name="student_id" class="form-control" value="{{ student.student_id }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Gender</label>
                                        <select name="gender" class="form-control">
                                            <option value="Female" {% if student.gender == 'Female' %}selected{% endif %}>Female</option>
                                            <option value="Male" {% if student.gender == 'Male' %}selected{% endif %}>Male</option>
                                            <option value="Others" {% if student.gender == 'Others' %}selected{% endif %}>Others</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Date of Birth</label>
                                        <input type="date" name="date_of_birth" class="form-control" value="{{ student.date_of_birth|date:'Y-m-d' }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Class</label>
                                        <input type="text" name="student_class" class="form-control" value="{{ student.student_class }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Religion</label>
                                        <input type="text" name="religion" class="form-control" value="{{ student.religion }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Joining Date</label>
                                        <input type="date" name="joining_date" class="form-control" value="{{ student.joining_date|date:'Y-m-d' }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Mobile Number</label>
                                        <input type="text" name="mobile_number" class="form-control" value="{{ student.mobile_number }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Admission Number</label>
                                        <input type="text" name="admission_number" class="form-control" value="{{ student.admission_number }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Section</label>
                                        <input type="text" name="section" class="form-control" value="{{ student.section }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Student Image</label>
                                        <input type="file" name="student_image" class="form-control">
                                        <small>Current Image: {{ student.student_image.url }}</small>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <h5 class="form-title"><span>Parent Information</span></h5>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Father's Name</label>
                                        <input type="text" name="father_name" class="form-control" value="{{ parent.father_name }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Father's Occupation</label>
                                        <input type="text" name="father_occupation" class="form-control" value="{{ parent.father_occupation }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Father's Mobile</label>
                                        <input type="text" name="father_mobile" class="form-control" value="{{ parent.father_mobile }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Father's Email</label>
                                        <input type="email" name="father_email" class="form-control" value="{{ parent.father_email }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Mother's Name</label>
                                        <input type="text" name="mother_name" class="form-control" value="{{ parent.mother_name }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Mother's Occupation</label>
                                        <input type="text" name="mother_occupation" class="form-control" value="{{ parent.mother_occupation }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Mother's Mobile</label>
                                        <input type="text" name="mother_mobile" class="form-control" value="{{ parent.mother_mobile }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Mother's Email</label>
                                        <input type="email" name="mother_email" class="form-control" value="{{ parent.mother_email }}">
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Present Address</label>
                                        <textarea name="present_address" class="form-control">{{ parent.present_address }}</textarea>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <label>Permanent Address</label>
                                        <textarea name="permanent_address" class="form-control">{{ parent.permanent_address }}</textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{% static 'assets/js/jquery-3.6.0.min.js' %}"></script>
<script src="{% static 'assets/js/popper.min.js' %}"></script>
<script src="{% static 'assets/plugins/bootstrap/js/bootstrap.min.js' %}"></script>
<script src="{% static 'assets/plugins/slimscroll/jquery.slimscroll.min.js' %}"></script>
<script src="{% static 'assets/js/script.js' %}"></script>
{% endblock %}
