{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<title>Preskool - Register</title>

<!-- Favicon -->
<link rel="shortcut icon" href="{% static 'assets/img/favicon.png' %}">
<!-- Google Fonts -->
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,500;0,600;0,700;1,400&display=swap">
<!-- Bootstrap CSS -->
<link rel="stylesheet" href="{% static 'assets/plugins/bootstrap/css/bootstrap.min.css' %}">
<!-- Font Awesome CSS -->
<link rel="stylesheet" href="{% static 'assets/plugins/fontawesome/css/fontawesome.min.css' %}">
<link rel="stylesheet" href="{% static 'assets/plugins/fontawesome/css/all.min.css' %}">
<!-- Custom CSS -->
<link rel="stylesheet" href="{% static 'assets/css/style.css' %}">
</head>
<body>

<div class="main-wrapper login-body">
    <div class="login-wrapper">
        <div class="container">
            <div class="loginbox">
                <div class="login-left">
                    <img class="img-fluid" src="{% static 'assets/img/logo-white.png' %}" alt="Logo">
                </div>
                <div class="login-right">
                    <div class="login-right-wrap">
                        <h1>Register</h1>
                        <p class="account-subtitle">Access to our dashboard</p>
                        
                        <!-- Error and success messages -->
                        {% if messages %}
                        <div class="alert alert-info">
                            {% for message in messages %}
                                <p>{{ message }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <!-- Registration Form -->
                        <form method="POST" action="{% url 'signup' %}">
                            {% csrf_token %}
                            <div class="form-group">
                                <input class="form-control" type="text" name="first_name" placeholder="First Name" required>
                            </div>
                            <div class="form-group">
                                <input class="form-control" type="text" name="last_name" placeholder="Last Name" required>
                            </div>
                            <div class="form-group">
                                <input class="form-control" type="email" name="email" placeholder="Email" required>
                            </div>
                            <div class="form-group">
                                <input class="form-control" type="password" name="password" placeholder="Password" required>
                            </div>
                            <div class="form-group">
                                <input class="form-control" type="password" name="confirm_password" placeholder="Confirm Password" required>
                            </div>
                            <div class="form-group mb-0">
                                <button class="btn btn-primary btn-block" type="submit">Register</button>
                            </div>
                        </form>

                        <div class="login-or">
                            <span class="or-line"></span>
                            <span class="span-or">or</span>
                        </div>

                        <div class="social-login">
                            <span>Register with</span>
                            <a href="#" class="facebook"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="google"><i class="fab fa-google"></i></a>
                        </div>

                        <div class="text-center dont-have">Already have an account? 
                            <a href="{% url 'login' %}">Login</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="{% static 'assets/js/jquery-3.6.0.min.js' %}"></script>
<!-- Bootstrap Core JS -->
<script src="{% static 'assets/js/popper.min.js' %}"></script>
<script src="{% static 'assets/plugins/bootstrap/js/bootstrap.min.js' %}"></script>
<!-- Custom JS -->
<script src="{% static 'assets/js/script.js' %}"></script>
</body>
</html>
