# Generated by Django 5.1.1 on 2024-10-06 07:30

import django.contrib.auth.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.Char<PERSON>ield(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "is_staff",
                    models.<PERSON>oleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                ("username", models.CharField(max_length=100, unique=True)),
                (
                    "email",
                    models.EmailField(db_index=True, max_length=255, unique=True),
                ),
                ("is_authorized", models.BooleanField(default=False)),
                ("login_token", models.CharField(blank=True, max_length=6, null=True)),
                ("first_name", models.CharField(blank=True, max_length=30)),
                ("last_name", models.CharField(blank=True, max_length=30)),
                ("date_joined", models.DateTimeField(auto_now_add=True)),
                ("is_student", models.BooleanField(default=False)),
                ("is_admin", models.BooleanField(default=False)),
                ("is_teacher", models.BooleanField(default=False)),
                ("groups", models.ManyToManyField(blank=True, to="auth.group")),
                (
                    "user_permissions",
                    models.ManyToManyField(blank=True, to="auth.permission"),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="PasswordResetRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                (
                    "token",
                    models.CharField(
                        default="EPkFaz5gGH7sctqBPr5A4dI5cFzjNyLb",
                        editable=False,
                        max_length=32,
                        unique=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="home_auth.customuser",
                    ),
                ),
            ],
        ),
    ]
