from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from .models import Notification

User = get_user_model()

class NotificationModelTest(TestCase):
    """Test cases for Notification model"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_notification_creation(self):
        """Test notification model creation"""
        notification = Notification.objects.create(
            user=self.user,
            message="Test notification message"
        )

        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.message, "Test notification message")
        self.assertFalse(notification.is_read)  # Default should be False
        self.assertTrue(notification.created_at)
        self.assertEqual(str(notification), "Test notification message")

class SchoolViewTest(TestCase):
    """Test cases for School views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            is_admin=True
        )

        self.notification = Notification.objects.create(
            user=self.user,
            message="Test notification"
        )

    def test_index_view(self):
        """Test index view"""
        response = self.client.get(reverse('index'))
        self.assertEqual(response.status_code, 200)

    def test_dashboard_view(self):
        """Test dashboard view"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('dashboard'))
        self.assertEqual(response.status_code, 200)

    def test_admin_dashboard_view(self):
        """Test admin dashboard view"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_dashboard'))
        self.assertEqual(response.status_code, 200)

    def test_teacher_dashboard_view(self):
        """Test teacher dashboard view"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('teacher_dashboard'))
        self.assertEqual(response.status_code, 200)

    def test_mark_notification_as_read(self):
        """Test marking notifications as read"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.post(reverse('mark_notification_as_read'))
        self.assertEqual(response.status_code, 200)

        # Check if notification was marked as read
        self.notification.refresh_from_db()
        self.assertTrue(self.notification.is_read)

    def test_clear_all_notifications(self):
        """Test clearing all notifications"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.post(reverse('clear_all_notification'))
        self.assertEqual(response.status_code, 200)

        # Check if notifications were deleted
        notifications_count = Notification.objects.filter(user=self.user).count()
        self.assertEqual(notifications_count, 0)
