<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from preschool.dreamguystech.com/html-template/assets/css/assets/img/html-template/library.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 28 Oct 2021 11:19:31 GMT -->
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>Preskool</title>
<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.html">
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,500;0,600;0,700;1,400&amp;display=swap" rel="stylesheet">
<link rel="stylesheet" href="assets/css/animate.html">
<link rel="stylesheet" href="assets/css/bootstrap.min.html">
<link rel="stylesheet" href="assets/css/materialdesignicons.min.html">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.html">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.html">
<link rel="stylesheet" href="assets/css/style.html">
</head>
<body>

<div id="loader-wrapper">
<div id="loader">
<div class="loader-ellips">
<span class="loader-ellips__dot"></span>
<span class="loader-ellips__dot"></span>
<span class="loader-ellips__dot"></span>
<span class="loader-ellips__dot"></span>
</div>
</div>
</div>

<header id="home" class="header">

<nav class="navbar navbar-expand-md home-menu hp4">
<div class="container-fluid">

<a class="logo-link smooth-menu header-logo" href="#home">
<img src="assets/img/logo.html" class="logo logo-display" alt="Logo">
</a>
<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-menu">
<i class="fa fa-bars"></i>
</button>

<div class="collapse navbar-collapse mainmenu" id="navbar-menu">
<ul class="nav navbar-nav ml-auto">
<li class="nav-item">
<a class="smooth-menu nav-link" href="#home">Home</a>
</li>
<li class="nav-item">
<a class="smooth-menu nav-link" href="#homepage">Home Page</a>
</li>
<li class="nav-item">
<a class="smooth-menu nav-link" href="#pages">Pages</a>
</li>
</ul>
<div class="nav-btn header-btn">
<a href="https://themeforest.net/item/preskool-bootstrap-admin-html-template/29532878" target="_blank" class="button download-btn">Buy Template</a>
</div>
</div>
</div>
</nav>

</header>

<section class="hero-section">
<div class="container-fluid">
<div class="row align-items-center">
<div class="col-lg-5 wow fadeInLeft" data-wow-delay="0.2s">
<h1 style="color:#fff">Preskool</h1>
<h2 style="color:#fff">Bootstrap HTML Admin Dashboard Template</h2>
<a href="#homepage" target="_blank" class="button">Live Demo</a>
<a href="https://themeforest.net/item/preskool-bootstrap-admin-html-template/29532878" target="_blank" class="button">Buy Template</a>
</div>
<div class="col-lg-7 wow fadeInRight" data-wow-delay="0.4s">
<div class="hero-4-img">
<a href="#" target="_blank"><img src="assets/img/image.html" alt=""></a>
</div>
</div>
</div>
</div>
</section>
<section id="homepage" class="home-page">
<div class="container-fluid">
<div class="row justify-content-center">
<div class="col-lg-8 text-center wow fadeInRight" data-wow-delay="0.2s">
<div class="section-header text-center">
<h2>Home Page</h2>
<p class="sub-title">We build this template with HTML5,Vuejs and Laravel.</p>
</div>
</div>
</div>
<div class="row demo-row justify-content-center">
<div class="col-md-8 col-sm-12 col-12">
<div class="demo-wrap mb-0" data-aos="fade-up">
<div class="demo-tabs">
<ul class="nav nav-tabs">
<li class="nav-item"><a target="_blank" href="html-template/index.html">HTML5</a></li>
<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/laravel/public/index">Laravel</a></li>
<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/vuejs/index">Vuejs</a></li>
<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/angular/preskool/">Angular</a></li>
<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/react/">Reactjs</a></li>
</ul>
</div>
<div class="demo-box">
<a target="_blank" href="#"><img class="img-fluid" src="assets/img/image.html" alt=""></a>
</div>
</div>
</div>
</div>
</div>
</section>
<section id="pages" class="doctor-features">
<div class="container-fluid">
<div class="row justify-content-center">
<div class="col-lg-8 text-center wow fadeInRight" data-wow-delay="0.2s">
<div class="section-header text-center">
<h2>Preskool Pages</h2>
<p class="sub-title">Preskool template includes various features for providers such as student list, teachers list, department,etc.</p>
</div>
</div>
</div>
<div class="row">
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" target="_blank" href="html-template/teachers.html"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-1.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/teachers.html" target="_blank">Teachers List</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/teacher-details.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-2.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/teacher-details.html" target="_blank">Teachers View</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/departments.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-3.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/departments.html" target="_blank">Departments</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/subjects.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-4.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/subjects.html" target="_blank">Subjects</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/fees-collections.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-5.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/fees-collections.html" target="_blank">Fees</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/expenses.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-6.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/expenses.html" target="_blank"> Expenses</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/salary.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-7.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/salary.html" target="_blank">Salary</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/holiday.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-8.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/holiday.html" target="_blank">Holiday</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/event.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-9.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/event.html" target="_blank">Events</a></div>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
<div class="project-link"><a class="button" href="html-template/library.html" target="_blank"> View Demo</a></div>
<a class="overlay-link" href="#" target="_blank"><img src="assets/img/image-10.html" alt="Project Image" /></a>
</div>
<div class="project-info">
<div class="project-title"><a href="html-template/library.html" target="_blank">Library</a></div>
</div>
</div>
</div>
</div>
</section>
<footer class="footer">

<div class="footer-bottom">
<div class="container-fluid">

<div class="copyright">
<div class="row">
<div class="col-md-12 text-center">
<div class="copyright-text">
<p class="mb-0">© 2020 <a href="index.html">Preskool</a>. All rights reserved.</p>
</div>
</div>
</div>
</div>

</div>
</div>

</footer>
<script src="assets/js/jquery-1.12.4.min.html"></script>
<script src="assets/js/popper.min.html"></script>
<script src="assets/js/bootstrap.min.html"></script>
<script src="assets/js/wow.min.html"></script>
<script src="assets/js/smooth-scroll.min.html"></script>
<script src="assets/js/main.html"></script>

<script type="text/javascript">
	var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
	(function(){
	var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
	s1.async=true;
	s1.src='https://embed.tawk.to/5d8a11a26c1dde20ed0329dd/default';
	s1.charset='UTF-8';
	s1.setAttribute('crossorigin','*');
	s0.parentNode.insertBefore(s1,s0);
	})();
	</script>

</body>

<!-- Mirrored from preschool.dreamguystech.com/html-template/assets/css/assets/img/html-template/library.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 28 Oct 2021 11:19:31 GMT -->
</html>