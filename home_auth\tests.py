from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from .models import CustomUser, PasswordResetRequest

User = get_user_model()

class CustomUserModelTest(TestCase):
    """Test cases for CustomUser model"""

    def test_user_creation(self):
        """Test custom user creation"""
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        self.assertEqual(user.username, '<EMAIL>')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertFalse(user.is_student)
        self.assertFalse(user.is_teacher)
        self.assertFalse(user.is_admin)
        self.assertFalse(user.is_authorized)

    def test_user_roles(self):
        """Test user role assignment"""
        # Test admin user
        admin_user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='adminpass123',
            is_admin=True
        )
        self.assertTrue(admin_user.is_admin)

        # Test teacher user
        teacher_user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='teacherpass123',
            is_teacher=True
        )
        self.assertTrue(teacher_user.is_teacher)

        # Test student user
        student_user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='studentpass123',
            is_student=True
        )
        self.assertTrue(student_user.is_student)

class AuthenticationViewTest(TestCase):
    """Test cases for Authentication views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            is_admin=True
        )

    def test_login_view_get(self):
        """Test login view GET request"""
        response = self.client.get(reverse('index'))
        self.assertEqual(response.status_code, 200)

    def test_login_view_post_valid(self):
        """Test login view with valid credentials"""
        response = self.client.post(reverse('index'), {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        # Should redirect after successful login
        self.assertEqual(response.status_code, 302)

    def test_login_view_post_invalid(self):
        """Test login view with invalid credentials"""
        response = self.client.post(reverse('index'), {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, 200)

    def test_signup_view_get(self):
        """Test signup view GET request"""
        response = self.client.get(reverse('signup'))
        self.assertEqual(response.status_code, 200)

class PasswordResetTest(TestCase):
    """Test cases for Password Reset functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_password_reset_request_creation(self):
        """Test password reset request creation"""
        reset_request = PasswordResetRequest.objects.create(
            user=self.user,
            email=self.user.email
        )

        self.assertEqual(reset_request.user, self.user)
        self.assertEqual(reset_request.email, self.user.email)
        self.assertTrue(reset_request.token)
        self.assertTrue(reset_request.is_valid())  # Should be valid when just created
