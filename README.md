

# **Student Management System**

A comprehensive **Student Management System** built using **Django**, designed to manage student information, including enrollment, attendance, grades, and other administrative functionalities. This application demonstrates modern web development practices and is ideal for schools or educational institutions looking for a streamlined solution to manage student data.

**Developed by:** [Your Name]
**Project Type:** Internship Project
**Technology Stack:** Django, Python, SQLite, HTML5, CSS3, JavaScript, Bootstrap
**Development Period:** [Your Development Period]

## **Table of Contents**
- [Features](#features)
- [Technologies Used](#technologies-used)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Running the Project](#running-the-project)
- [Screenshots](#screenshots)
- [Testing](#testing)
- [Contributing](#contributing)
- [License](#license)

## **Features**
- **Student Enrollment**: Complete student registration system with parent information
- **Student Management**: Add, edit, view, and delete student records
- **Role-Based Access Control**: Separate dashboards for Admin, Teachers, and Students
- **Real-time Notifications**: System-wide notification system for user actions
- **Image Upload**: Student profile picture management
- **Search & Filter**: Easy student record navigation
- **Responsive Design**: Mobile-friendly interface using Bootstrap
- **Data Validation**: Comprehensive form validation and error handling
- **Secure Authentication**: Custom user model with role-based permissions

## **Technologies Used**
- **Backend**: Django 5.1.1 (Python), Django ORM
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 4
- **Database**: SQLite (development), PostgreSQL (production ready)
- **Authentication**: Custom User Model with role-based access
- **File Handling**: Django's ImageField for profile pictures
- **Notifications**: Real-time notification system
- **Version Control**: Git
- **Deployment**: Docker, Gunicorn, WhiteNoise for static files
- **Testing**: Django's built-in testing framework

## **Key Technical Implementations**
- **Custom User Model**: Extended Django's AbstractUser for role-based authentication
- **Model Relationships**: OneToOne relationships between Student and Parent models
- **Slug Generation**: Automatic slug generation for SEO-friendly URLs
- **File Upload**: Secure image upload with proper validation
- **AJAX Notifications**: Real-time notification marking without page refresh
- **Responsive Design**: Mobile-first approach using Bootstrap grid system
- **Form Validation**: Both client-side and server-side validation
- **Security**: CSRF protection, secure file uploads, and proper authentication

## **Project Structure**
```
student-management-system/
│
├── manage.py               # Django project manager script
├── requirements.txt        # Project dependencies
├── Home/                   # Main Django project configuration
│   ├── settings.py         # Project settings
│   ├── urls.py            # Main URL configuration
│   └── wsgi.py            # WSGI configuration
├── home_auth/             # Custom authentication app
│   ├── models.py          # Custom user model
│   ├── views.py           # Authentication views
│   └── urls.py            # Auth URL patterns
├── school/                # School management app
│   ├── models.py          # Notification model
│   ├── views.py           # Dashboard and notification views
│   └── urls.py            # School URL patterns
├── student/               # Student management app
│   ├── models.py          # Student and Parent models
│   ├── views.py           # Student CRUD operations
│   └── urls.py            # Student URL patterns
├── templates/             # HTML templates
│   ├── authentication/    # Login/register templates
│   ├── students/          # Student management templates
│   └── Home/              # Base templates
├── static/                # Static files (CSS, JS, images)
└── media/                 # User uploaded files
```

## **Installation**
Follow the steps below to get the project up and running on your local machine:

### **1. Clone the Repository**
```bash
git clone https://github.com/nilkanth02/student-management-system.git
cd student-management-system
```

### **2. Create a Virtual Environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\activate
```

### **3. Install Dependencies**
```bash
pip install -r requirements.txt
```

### **4. Set Up Database**
Make sure to set up your database (e.g., PostgreSQL) and update the `DATABASES` configuration in `student_management/settings.py`.

### **5. Run Migrations**
```bash
python manage.py migrate
```

### **6. Create Superuser**
```bash
python manage.py createsuperuser
```

### **7. Run the Application**
```bash
python manage.py runserver
```
Visit `http://localhost:8000` to access the app.

## **Configuration**
You'll need a `.env` file for environment-specific configurations. Example:

```bash
DEBUG=True
SECRET_KEY='your_secret_key'
DATABASE_URL=postgres://user:password@localhost:5432/your_db_name
```

Make sure to configure settings like database, static files, and email backend properly for production.

## **Running the Project with Docker (Optional)**
If you've Dockerized the project, you can run it as follows:

```bash
docker-compose up --build
```
This will build the Docker image and run the Django application and the PostgreSQL service.

## **Video Links**
Watch the videos to learn in proper way.
https://youtu.be/mM6vMMLYJHY


## **Testing**
You can run the unit tests with Django's built-in testing framework:

```bash
python manage.py test
```

This will run all the tests located in the `tests.py` files of your Django apps.

## **Contributing**
If you want to contribute to this project, please follow the steps below:
1. Fork the repository.
2. Create a new branch (`git checkout -b feature/your-feature-name`).
3. Commit your changes (`git commit -am 'Add a new feature'`).
4. Push to the branch (`git push origin feature/your-feature-name`).
5. Create a pull request.




